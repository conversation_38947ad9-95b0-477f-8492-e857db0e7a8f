#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
a_result.xlsx 파일에서 구성품별 비율 계산 프로그램
"""

import pandas as pd
import numpy as np
from pathlib import Path

def calculate_component_ratio(input_file='a_result.xlsx'):
    """
    Excel 파일에서 구성품별 비율을 계산합니다.
    
    Args:
        input_file (str): 입력 Excel 파일명
    """
    
    try:
        print(f"📊 구성품별 비율 계산 프로그램")
        print("=" * 50)
        print(f"입력 파일: {input_file}")
        
        # Excel 파일 읽기 (1번 행이 헤더)
        df = pd.read_excel(input_file, sheet_name='Sheet1', engine='openpyxl')
        
        print(f"\n📋 현재 데이터:")
        print(df)
        
        # 전체 수량 계산
        total_quantity = df['누적'].sum()
        print(f"\n📈 전체 수량: {total_quantity}")
        
        # 각 구성품별 비율 계산
        df['비율(%)'] = (df['누적'] / total_quantity * 100).round(2)
        df['비율(소수)'] = (df['누적'] / total_quantity).round(4)
        
        print(f"\n📊 구성품별 비율:")
        print("=" * 80)
        
        # 결과를 보기 좋게 출력
        for idx, row in df.iterrows():
            구성품 = row['구성품']
            누적 = row['누적']
            비율_퍼센트 = row['비율(%)']
            비율_소수 = row['비율(소수)']
            
            print(f"{idx+1}. {구성품}")
            print(f"   - 수량: {누적}")
            print(f"   - 비율: {비율_퍼센트}% ({비율_소수})")
            print(f"   - 계산: {누적} ÷ {total_quantity} = {비율_소수}")
            print()
        
        # 비율 합계 검증
        ratio_sum = df['비율(%)'].sum()
        print(f"🔍 비율 합계 검증: {ratio_sum}% (100%에 가까워야 함)")
        
        # 결과 테이블 출력
        print(f"\n📋 최종 결과 테이블:")
        print("=" * 80)
        result_df = df[['구성품', '누적', '비율(%)', '비율(소수)']]
        print(result_df.to_string(index=False))
        
        return result_df
        
    except FileNotFoundError:
        print(f"❌ 오류: 파일 '{input_file}'을 찾을 수 없습니다.")
        return None
    except KeyError as e:
        print(f"❌ 오류: 필요한 열을 찾을 수 없습니다: {e}")
        print("파일에 '구성품'과 '누적' 열이 있는지 확인해주세요.")
        return None
    except Exception as e:
        print(f"❌ 예상치 못한 오류가 발생했습니다: {str(e)}")
        return None

def create_ratio_commands():
    """
    비율 계산을 위한 다양한 명령어들을 제공합니다.
    """
    
    print(f"\n💡 비율 계산 명령어 모음:")
    print("=" * 60)
    
    commands = [
        {
            "설명": "기본 비율 계산 (퍼센트)",
            "명령어": "python -c \"import pandas as pd; df = pd.read_excel('a_result.xlsx', sheet_name='Sheet1', engine='openpyxl'); total = df['누적'].sum(); df['비율(%)'] = (df['누적'] / total * 100).round(2); print(df[['구성품', '누적', '비율(%)']])\""
        },
        {
            "설명": "비율 계산 (소수점)",
            "명령어": "python -c \"import pandas as pd; df = pd.read_excel('a_result.xlsx', sheet_name='Sheet1', engine='openpyxl'); total = df['누적'].sum(); df['비율'] = (df['누적'] / total).round(4); print(df[['구성품', '누적', '비율']])\""
        },
        {
            "설명": "전체 수량 확인",
            "명령어": "python -c \"import pandas as pd; df = pd.read_excel('a_result.xlsx', sheet_name='Sheet1', engine='openpyxl'); print(f'전체 수량: {df[\\\"누적\\\"].sum()}')\""
        },
        {
            "설명": "상세 비율 정보",
            "명령어": "python calculate_ratio.py"
        }
    ]
    
    for i, cmd in enumerate(commands, 1):
        print(f"{i}. {cmd['설명']}")
        print(f"   {cmd['명령어']}")
        print()

if __name__ == "__main__":
    # 메인 비율 계산 실행
    result = calculate_component_ratio()
    
    if result is not None:
        # 명령어 모음 출력
        create_ratio_commands()
    
    print("\n" + "=" * 60)
    print("프로그램 종료")
    print("=" * 60)
