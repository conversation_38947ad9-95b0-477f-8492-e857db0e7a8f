#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tender 분석 결과 추가 명령어들
"""

import pandas as pd
import numpy as np

def create_simple_discount_file():
    """1% 단위 할인 계산 결과 파일 생성 (1%~20%)"""

    # 원본 데이터 읽기
    df_raw = pd.read_excel('tender.xlsx', sheet_name='Repair 서비스 단가표', engine='openpyxl', header=None)

    # 헤더 설정
    headers = df_raw.iloc[11].tolist()
    data_df = df_raw.iloc[12:].copy()
    data_df.columns = [f'col_{i}' if pd.isna(h) else str(h).replace('\n', '_').strip()
                      for i, h in enumerate(headers)]

    # 유효한 데이터 필터링
    valid_rows = data_df[
        (data_df['제품군'].notna() & (data_df['제품군'] != 'Tier 1') & (data_df['제품군'] != 'Tier 2')) |
        (data_df['단가 구분'].notna() & (data_df['단가 구분'] != ''))
    ].copy()

    # 주요 컬럼들 찾기
    price_columns = []
    for col in data_df.columns:
        if '수리' in str(col) or '검수' in str(col):
            if any(char.isdigit() for char in str(col)) == False:  # 숫자가 없는 컬럼명
                price_columns.append(col)

    # 결과 데이터 생성
    result_data = []

    for idx, row in valid_rows.iterrows():
        base_info = {
            '제품군': row['제품군'] if pd.notna(row['제품군']) else '',
            '구분': row.iloc[2] if pd.notna(row.iloc[2]) else '',
            '세부구분': row['단가 구분'] if pd.notna(row['단가 구분']) else ''
        }

        # 각 가격 컬럼에 대해 1%부터 20%까지 할인 계산
        for col in price_columns:
            if col in row.index:
                try:
                    original_price = float(row[col]) if pd.notna(row[col]) else 0
                except:
                    original_price = 0

                base_info[f'{col}_원본'] = original_price

                # 1%부터 20%까지 1% 단위로 할인 계산
                for percent in range(1, 21):
                    discount_rate = percent / 100
                    discounted_value = original_price * (1 - discount_rate)
                    base_info[f'{col}_{percent}%할인'] = round(discounted_value, 0)

        result_data.append(base_info)

    # 결과 저장
    result_df = pd.DataFrame(result_data)
    result_df.to_excel('tender_1percent_discount.xlsx', index=False)

    print(f"1% 단위 할인 계산 파일 생성 완료: tender_1percent_discount.xlsx")
    print(f"데이터 행 수: {len(result_df)}")
    print(f"컬럼 수: {len(result_df.columns)}")
    print(f"할인율: 1%부터 20%까지 (1% 단위)")

    return result_df

def create_summary_by_product():
    """제품군별 요약 파일 생성"""
    
    # 분석 결과 읽기
    df = pd.read_excel('tender_analysis_result.xlsx', sheet_name='전체결과', engine='openpyxl')
    
    # 제품군별 그룹화
    summary_data = []
    
    for product_group in df['제품군'].unique():
        if pd.notna(product_group) and product_group != '':
            group_data = df[df['제품군'] == product_group]
            
            summary_row = {
                '제품군': product_group,
                '제품수': len(group_data),
                '평균_수리부품교체_원본': group_data['수리_부품교체(수리비용)_원본'].mean(),
                '평균_수리부품교체_10%할인': group_data['수리_부품교체(수리비용)_10%차감'].mean(),
                '평균_수리부품교체_20%할인': group_data['수리_부품교체(수리비용)_20%차감'].mean()
            }
            
            summary_data.append(summary_row)
    
    summary_df = pd.DataFrame(summary_data)
    summary_df.to_excel('tender_product_summary.xlsx', index=False)
    
    print(f"제품군별 요약 파일 생성 완료: tender_product_summary.xlsx")
    print(f"제품군 수: {len(summary_df)}")
    
    return summary_df

def create_detailed_discount_table():
    """상세한 1% 단위 할인 테이블 생성 (원본 데이터 기반)"""

    # 원본 데이터 읽기
    df_raw = pd.read_excel('tender.xlsx', sheet_name='Repair 서비스 단가표', engine='openpyxl', header=None)

    # 헤더 설정
    headers = df_raw.iloc[11].tolist()
    data_df = df_raw.iloc[12:].copy()
    data_df.columns = [f'col_{i}' if pd.isna(h) else str(h).replace('\n', '_').strip()
                      for i, h in enumerate(headers)]

    # 유효한 데이터 필터링
    valid_rows = data_df[
        (data_df['제품군'].notna() & (data_df['제품군'] != 'Tier 1') & (data_df['제품군'] != 'Tier 2')) |
        (data_df['단가 구분'].notna() & (data_df['단가 구분'] != ''))
    ].copy()

    # 주요 컬럼들 찾기 (수리 관련 컬럼들)
    target_columns = [
        '수리_부품교체_(수리비용)',
        '수리_세척(신규)',
        '수리_S/W',
        '수리_기타',
        '검수(점검)'
    ]

    # 실제 존재하는 컬럼들 매핑
    column_mapping = {}
    for target_col in target_columns:
        for actual_col in data_df.columns:
            actual_col_clean = str(actual_col).replace('_', '').replace('(', '').replace(')', '').replace(' ', '')
            target_col_clean = target_col.replace('_', '').replace('(', '').replace(')', '').replace(' ', '')

            if target_col_clean in actual_col_clean or actual_col_clean in target_col_clean:
                column_mapping[target_col] = actual_col
                break

    print(f"매핑된 컬럼들: {column_mapping}")

    # 결과 데이터 생성
    result_data = []

    for idx, row in valid_rows.iterrows():
        base_info = {
            '제품군': row['제품군'] if pd.notna(row['제품군']) else '',
            '구분': row.iloc[2] if pd.notna(row.iloc[2]) else '',
            '세부구분': row['단가 구분'] if pd.notna(row['단가 구분']) else ''
        }

        # 각 매핑된 컬럼에 대해 1%부터 20%까지 할인 계산
        for target_col, actual_col in column_mapping.items():
            if actual_col in row.index:
                try:
                    # 숫자 변환 시도
                    original_value = row[actual_col]
                    if pd.notna(original_value):
                        if isinstance(original_value, str):
                            import re
                            numbers = re.findall(r'\d+', str(original_value))
                            if numbers:
                                original_value = float(numbers[0])
                            else:
                                original_value = 0
                        elif isinstance(original_value, (int, float)):
                            original_value = float(original_value)
                        else:
                            original_value = 0
                    else:
                        original_value = 0
                except:
                    original_value = 0

                base_info[f'{target_col}_원본'] = original_value

                # 1%부터 20%까지 1% 단위로 할인 계산
                for percent in range(1, 21):
                    discount_rate = percent / 100
                    discounted_value = original_value * (1 - discount_rate)
                    base_info[f'{target_col}_{percent}%할인'] = round(discounted_value, 0)

        result_data.append(base_info)

    # 결과 저장
    result_df = pd.DataFrame(result_data)
    result_df.to_excel('tender_detailed_1percent_discount.xlsx', index=False)

    print(f"상세한 1% 단위 할인 테이블 생성 완료: tender_detailed_1percent_discount.xlsx")
    print(f"데이터 행 수: {len(result_df)}")
    print(f"컬럼 수: {len(result_df.columns)}")
    print(f"할인율: 1%부터 20%까지 (1% 단위)")
    print(f"분석 컬럼: {list(column_mapping.keys())}")

    return result_df

def print_commands():
    """사용 가능한 명령어들 출력"""
    
    print("=" * 70)
    print("📊 Tender 분석 추가 명령어 모음")
    print("=" * 70)
    
    commands = [
        {
            "설명": "1% 단위 할인 계산 파일 생성 (1%~20%까지 모든 할인율)",
            "명령어": "python -c \"from tender_commands import create_simple_discount_file; create_simple_discount_file()\""
        },
        {
            "설명": "상세한 1% 단위 할인 테이블 생성 (지정된 컬럼들만)",
            "명령어": "python -c \"from tender_commands import create_detailed_discount_table; create_detailed_discount_table()\""
        },
        {
            "설명": "제품군별 요약 파일 생성",
            "명령어": "python -c \"from tender_commands import create_summary_by_product; create_summary_by_product()\""
        },
        {
            "설명": "특정 할인율만 CSV로 추출 (예: 10% 할인)",
            "명령어": "python -c \"import pandas as pd; df = pd.read_excel('tender_analysis_result.xlsx', sheet_name='전체결과'); cols = ['제품군', '구분', '세부구분'] + [col for col in df.columns if '10%차감' in col]; result = df[cols]; result.to_csv('tender_10percent_discount.csv', index=False, encoding='utf-8-sig'); print('10% 할인 파일 생성: tender_10percent_discount.csv')\""
        },
        {
            "설명": "특정 할인율만 Excel로 추출 (예: 15% 할인)",
            "명령어": "python -c \"import pandas as pd; df = pd.read_excel('tender_analysis_result.xlsx', sheet_name='전체결과'); cols = ['제품군', '구분', '세부구분'] + [col for col in df.columns if '15%차감' in col]; result = df[cols]; result.to_excel('tender_15percent_discount.xlsx', index=False); print('15% 할인 파일 생성: tender_15percent_discount.xlsx')\""
        },
        {
            "설명": "원본 가격만 추출",
            "명령어": "python -c \"import pandas as pd; df = pd.read_excel('tender_analysis_result.xlsx', sheet_name='전체결과'); cols = ['제품군', '구분', '세부구분'] + [col for col in df.columns if '원본' in col]; result = df[cols]; result.to_excel('tender_original_prices.xlsx', index=False); print('원본 가격 파일 생성: tender_original_prices.xlsx')\""
        },
        {
            "설명": "모든 할인율을 하나의 CSV 파일로 추출",
            "명령어": "python -c \"import pandas as pd; df = pd.read_excel('tender_analysis_result.xlsx', sheet_name='전체결과'); df.to_csv('tender_all_discounts.csv', index=False, encoding='utf-8-sig'); print('전체 할인율 CSV 파일 생성: tender_all_discounts.csv')\""
        },
        {
            "설명": "전체 분석 재실행 (1%~20% 모든 할인율)",
            "명령어": "python tender_analysis.py"
        }
    ]
    
    for i, cmd in enumerate(commands, 1):
        print(f"{i}. {cmd['설명']}")
        print(f"   {cmd['명령어']}")
        print()

if __name__ == "__main__":
    print_commands()
