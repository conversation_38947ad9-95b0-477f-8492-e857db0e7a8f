#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tender 분석 결과 추가 명령어들
"""

import pandas as pd
import numpy as np

def create_simple_discount_file():
    """간단한 할인 계산 결과 파일 생성"""
    
    # 원본 데이터 읽기
    df_raw = pd.read_excel('tender.xlsx', sheet_name='Repair 서비스 단가표', engine='openpyxl', header=None)
    
    # 헤더 설정
    headers = df_raw.iloc[11].tolist()
    data_df = df_raw.iloc[12:].copy()
    data_df.columns = [f'col_{i}' if pd.isna(h) else str(h).replace('\n', '_').strip() 
                      for i, h in enumerate(headers)]
    
    # 유효한 데이터 필터링
    valid_rows = data_df[
        (data_df['제품군'].notna() & (data_df['제품군'] != 'Tier 1') & (data_df['제품군'] != 'Tier 2')) |
        (data_df['단가 구분'].notna() & (data_df['단가 구분'] != ''))
    ].copy()
    
    # 주요 컬럼들 찾기
    price_columns = []
    for col in data_df.columns:
        if '수리' in str(col) or '검수' in str(col):
            if any(char.isdigit() for char in str(col)) == False:  # 숫자가 없는 컬럼명
                price_columns.append(col)
    
    # 결과 데이터 생성
    result_data = []
    
    for idx, row in valid_rows.iterrows():
        base_info = {
            '제품군': row['제품군'] if pd.notna(row['제품군']) else '',
            '구분': row.iloc[2] if pd.notna(row.iloc[2]) else '',
            '세부구분': row['단가 구분'] if pd.notna(row['단가 구분']) else ''
        }
        
        # 각 가격 컬럼에 대해 5%, 10%, 15%, 20% 할인 계산
        for col in price_columns:
            if col in row.index:
                try:
                    original_price = float(row[col]) if pd.notna(row[col]) else 0
                except:
                    original_price = 0
                
                base_info[f'{col}_원본'] = original_price
                base_info[f'{col}_5%할인'] = round(original_price * 0.95, 0)
                base_info[f'{col}_10%할인'] = round(original_price * 0.90, 0)
                base_info[f'{col}_15%할인'] = round(original_price * 0.85, 0)
                base_info[f'{col}_20%할인'] = round(original_price * 0.80, 0)
        
        result_data.append(base_info)
    
    # 결과 저장
    result_df = pd.DataFrame(result_data)
    result_df.to_excel('tender_simple_discount.xlsx', index=False)
    
    print(f"간단한 할인 계산 파일 생성 완료: tender_simple_discount.xlsx")
    print(f"데이터 행 수: {len(result_df)}")
    print(f"컬럼 수: {len(result_df.columns)}")
    
    return result_df

def create_summary_by_product():
    """제품군별 요약 파일 생성"""
    
    # 분석 결과 읽기
    df = pd.read_excel('tender_analysis_result.xlsx', sheet_name='전체결과', engine='openpyxl')
    
    # 제품군별 그룹화
    summary_data = []
    
    for product_group in df['제품군'].unique():
        if pd.notna(product_group) and product_group != '':
            group_data = df[df['제품군'] == product_group]
            
            summary_row = {
                '제품군': product_group,
                '제품수': len(group_data),
                '평균_수리부품교체_원본': group_data['수리_부품교체(수리비용)_원본'].mean(),
                '평균_수리부품교체_10%할인': group_data['수리_부품교체(수리비용)_10%차감'].mean(),
                '평균_수리부품교체_20%할인': group_data['수리_부품교체(수리비용)_20%차감'].mean()
            }
            
            summary_data.append(summary_row)
    
    summary_df = pd.DataFrame(summary_data)
    summary_df.to_excel('tender_product_summary.xlsx', index=False)
    
    print(f"제품군별 요약 파일 생성 완료: tender_product_summary.xlsx")
    print(f"제품군 수: {len(summary_df)}")
    
    return summary_df

def print_commands():
    """사용 가능한 명령어들 출력"""
    
    print("=" * 70)
    print("📊 Tender 분석 추가 명령어 모음")
    print("=" * 70)
    
    commands = [
        {
            "설명": "간단한 할인 계산 파일 생성 (5%, 10%, 15%, 20%)",
            "명령어": "python -c \"from tender_commands import create_simple_discount_file; create_simple_discount_file()\""
        },
        {
            "설명": "제품군별 요약 파일 생성",
            "명령어": "python -c \"from tender_commands import create_summary_by_product; create_summary_by_product()\""
        },
        {
            "설명": "특정 할인율만 CSV로 추출 (예: 10% 할인)",
            "명령어": "python -c \"import pandas as pd; df = pd.read_excel('tender_analysis_result.xlsx', sheet_name='전체결과'); cols = ['제품군', '구분', '세부구분'] + [col for col in df.columns if '10%차감' in col]; result = df[cols]; result.to_csv('tender_10percent_discount.csv', index=False, encoding='utf-8-sig'); print('10% 할인 파일 생성: tender_10percent_discount.csv')\""
        },
        {
            "설명": "원본 가격만 추출",
            "명령어": "python -c \"import pandas as pd; df = pd.read_excel('tender_analysis_result.xlsx', sheet_name='전체결과'); cols = ['제품군', '구분', '세부구분'] + [col for col in df.columns if '원본' in col]; result = df[cols]; result.to_excel('tender_original_prices.xlsx', index=False); print('원본 가격 파일 생성: tender_original_prices.xlsx')\""
        },
        {
            "설명": "전체 분석 재실행",
            "명령어": "python tender_analysis.py"
        }
    ]
    
    for i, cmd in enumerate(commands, 1):
        print(f"{i}. {cmd['설명']}")
        print(f"   {cmd['명령어']}")
        print()

if __name__ == "__main__":
    print_commands()
