#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
tender.xlsx 파일 디버깅 스크립트
"""

import pandas as pd
import numpy as np

def debug_tender():
    df = pd.read_excel('tender.xlsx', sheet_name='Repair 서비스 단가표', engine='openpyxl', header=None)
    
    print("12행부터 처음 10행:")
    data_df = df.iloc[12:]
    
    for i in range(min(10, len(data_df))):
        row_data = []
        for j in range(min(5, len(data_df.columns))):
            val = data_df.iloc[i, j]
            if pd.notna(val):
                row_data.append(str(val)[:20])
            else:
                row_data.append("NaN")
        print(f"행 {i}: {row_data}")
    
    print(f"\n첫 번째 컬럼의 유니크 값들:")
    first_col_values = data_df.iloc[:, 0].dropna().unique()
    for val in first_col_values[:20]:
        print(f"  '{val}'")

if __name__ == "__main__":
    debug_tender()
