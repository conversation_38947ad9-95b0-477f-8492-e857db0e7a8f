#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel 파일 처리 스크립트
a.xlsx 파일의 Sheet1 시트의 A열 데이터를 분석하여 
쉼표로 분리된 값들의 빈도를 계산하고 결과를 저장합니다.
"""

import pandas as pd
from collections import Counter
import os
import sys

def process_excel_file(input_file='a.xlsx', output_file='result.xlsx'):
    """
    Excel 파일을 처리하여 A열의 데이터를 분석합니다.
    
    Args:
        input_file (str): 입력 Excel 파일명
        output_file (str): 출력 Excel 파일명
    """
    
    try:
        # Excel 파일 읽기
        print(f"'{input_file}' 파일을 읽는 중...")
        df = pd.read_excel(input_file, sheet_name='Sheet1', engine='openpyxl')
        
        # A열 데이터 확인 - 실제 데이터가 있는 열 찾기
        print(f"열 이름들: {list(df.columns)}")

        # 각 열에서 null이 아닌 값의 개수 확인
        for i, col in enumerate(df.columns):
            non_null_count = df[col].notna().sum()
            print(f"열 {i+1} ('{col}'): {non_null_count}개의 유효한 값")

        # 데이터가 있는 첫 번째 열 찾기
        a_column = None
        for col in df.columns:
            if df[col].notna().sum() > 0:
                a_column = df[col]
                print(f"데이터 분석 대상 열: '{col}'")
                break

        if a_column is None:
            print("오류: 유효한 데이터가 있는 열을 찾을 수 없습니다.")
            return None
        
        print(f"A열에서 {len(a_column)} 개의 행을 발견했습니다.")
        
        # 모든 값들을 수집할 리스트
        all_values = []
        
        # 각 셀의 값을 처리
        for idx, cell_value in enumerate(a_column):
            if pd.notna(cell_value):  # NaN이 아닌 경우만 처리
                # 문자열로 변환
                cell_str = str(cell_value)
                
                # 쉼표로 분리하고 앞뒤 공백 제거
                split_values = [value.strip() for value in cell_str.split(',')]
                
                # 빈 문자열이 아닌 값들만 추가
                valid_values = [value for value in split_values if value]
                all_values.extend(valid_values)
                
                print(f"행 {idx + 1}: '{cell_str}' -> {valid_values}")
        
        print(f"\n총 {len(all_values)}개의 값을 추출했습니다.")
        
        # 값들의 빈도 계산
        counter = Counter(all_values)
        
        # 빈도가 높은 순서대로 정렬
        sorted_items = counter.most_common()
        
        print(f"\n고유한 값의 개수: {len(sorted_items)}")
        print("\n빈도 분석 결과:")
        for value, count in sorted_items[:10]:  # 상위 10개만 미리보기
            print(f"  '{value}': {count}회")
        
        # 결과 DataFrame 생성
        result_df = pd.DataFrame(sorted_items, columns=['값', '빈도'])
        
        # 원본 데이터와 결과를 함께 저장
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 원본 데이터 시트
            df.to_excel(writer, sheet_name='원본데이터', index=False)
            
            # 분석 결과 시트
            result_df.to_excel(writer, sheet_name='빈도분석결과', index=False)
            
            # 요약 정보 시트
            summary_df = pd.DataFrame({
                '항목': ['총 추출된 값 개수', '고유한 값 개수', '가장 많이 나타난 값', '최대 빈도'],
                '값': [
                    len(all_values),
                    len(sorted_items),
                    sorted_items[0][0] if sorted_items else 'N/A',
                    sorted_items[0][1] if sorted_items else 0
                ]
            })
            summary_df.to_excel(writer, sheet_name='요약', index=False)
        
        print(f"\n결과가 '{output_file}' 파일에 저장되었습니다.")
        print(f"파일에는 다음 시트들이 포함되어 있습니다:")
        print("  - 원본데이터: 원본 Excel 데이터")
        print("  - 빈도분석결과: 값별 빈도 (내림차순 정렬)")
        print("  - 요약: 분석 요약 정보")
        
        return result_df
        
    except FileNotFoundError:
        print(f"오류: '{input_file}' 파일을 찾을 수 없습니다.")
        return None
    except Exception as e:
        print(f"오류 발생: {str(e)}")
        return None

def main():
    """메인 함수"""
    print("Excel 파일 데이터 분석 도구")
    print("=" * 50)
    
    # 입력 파일 확인
    input_file = 'a.xlsx'
    if not os.path.exists(input_file):
        print(f"오류: '{input_file}' 파일이 존재하지 않습니다.")
        return
    
    # 처리 실행
    result = process_excel_file(input_file, 'result.xlsx')
    
    if result is not None:
        print("\n처리가 완료되었습니다!")
        print(f"결과 파일: result.xlsx")
    else:
        print("\n처리 중 오류가 발생했습니다.")

if __name__ == "__main__":
    main()
