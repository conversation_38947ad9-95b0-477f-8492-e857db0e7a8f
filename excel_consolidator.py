#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel 파일에서 구성품별 누적값 합산 및 중복 제거 프로그램
"""

import pandas as pd
import numpy as np
from pathlib import Path

def consolidate_excel_data(input_file='a.xlsx', output_file='a_result.xlsx'):
    """
    Excel 파일에서 구성품이 같은 항목들의 누적값을 합산하고 중복을 제거합니다.
    
    Args:
        input_file (str): 입력 Excel 파일명
        output_file (str): 출력 Excel 파일명
    """
    
    try:
        print(f"입력 파일 읽는 중: {input_file}")
        
        # Excel 파일 읽기 (1번 행이 헤더)
        df = pd.read_excel(input_file, sheet_name='Sheet1', engine='openpyxl')
        
        print(f"원본 데이터:")
        print(df)
        print(f"\n원본 데이터 행 수: {len(df)}")
        
        # 구성품별로 그룹화하여 누적값 합산
        print(f"\n구성품별 누적값 합산 중...")
        consolidated_df = df.groupby('구성품', as_index=False)['누적'].sum()
        
        # 결과 정렬 (구성품명 기준 오름차순)
        consolidated_df = consolidated_df.sort_values('구성품').reset_index(drop=True)
        
        print(f"\n합산 결과:")
        print(consolidated_df)
        print(f"\n결과 데이터 행 수: {len(consolidated_df)}")
        
        # 결과를 Excel 파일로 저장
        print(f"\n결과 파일 저장 중: {output_file}")
        consolidated_df.to_excel(output_file, sheet_name='Sheet1', index=False, engine='openpyxl')
        
        print(f"✅ 작업 완료!")
        print(f"   - 입력 파일: {input_file}")
        print(f"   - 출력 파일: {output_file}")
        print(f"   - 원본 행 수: {len(df)}")
        print(f"   - 결과 행 수: {len(consolidated_df)}")
        
        # 각 구성품별 합산 내역 출력
        print(f"\n📊 구성품별 합산 내역:")
        for _, row in consolidated_df.iterrows():
            구성품 = row['구성품']
            총누적 = row['누적']
            원본개수 = len(df[df['구성품'] == 구성품])
            print(f"   - {구성품}: {총누적} (원본 {원본개수}개 항목 합산)")
        
        return consolidated_df
        
    except FileNotFoundError:
        print(f"❌ 오류: 파일 '{input_file}'을 찾을 수 없습니다.")
        return None
    except KeyError as e:
        print(f"❌ 오류: 필요한 열을 찾을 수 없습니다: {e}")
        print("파일에 '구성품'과 '누적' 열이 있는지 확인해주세요.")
        return None
    except Exception as e:
        print(f"❌ 예상치 못한 오류가 발생했습니다: {str(e)}")
        return None

def verify_result(output_file='a_result.xlsx'):
    """
    결과 파일을 검증합니다.
    
    Args:
        output_file (str): 검증할 Excel 파일명
    """
    
    try:
        print(f"\n🔍 결과 파일 검증 중: {output_file}")
        
        if not Path(output_file).exists():
            print(f"❌ 결과 파일이 존재하지 않습니다: {output_file}")
            return False
        
        # 결과 파일 읽기
        result_df = pd.read_excel(output_file, sheet_name='Sheet1', engine='openpyxl')
        
        print(f"✅ 결과 파일 검증 완료:")
        print(f"   - 파일 크기: {Path(output_file).stat().st_size} bytes")
        print(f"   - 행 수: {len(result_df)}")
        print(f"   - 열 수: {len(result_df.columns)}")
        print(f"   - 열 이름: {list(result_df.columns)}")
        
        print(f"\n최종 결과 데이터:")
        print(result_df)
        
        return True
        
    except Exception as e:
        print(f"❌ 결과 파일 검증 중 오류 발생: {str(e)}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("📊 Excel 구성품별 누적값 합산 프로그램")
    print("=" * 60)
    
    # 메인 작업 실행
    result = consolidate_excel_data()
    
    if result is not None:
        # 결과 검증
        verify_result()
    
    print("\n" + "=" * 60)
    print("프로그램 종료")
    print("=" * 60)
