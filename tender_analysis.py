#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
tender.xlsx 파일 분석 및 1%씩 차감 계산 프로그램
"""

import pandas as pd
import numpy as np
from pathlib import Path

def analyze_tender_data(input_file='tender.xlsx', output_file='tender_analysis_result.xlsx'):
    """
    tender.xlsx 파일에서 지정된 컬럼들의 값을 1%씩 차감하여 분석합니다.
    
    Args:
        input_file (str): 입력 Excel 파일명
        output_file (str): 출력 Excel 파일명
    """
    
    try:
        print(f"📊 Tender 파일 분석 및 차감 계산 프로그램")
        print("=" * 60)
        print(f"입력 파일: {input_file}")
        print(f"출력 파일: {output_file}")
        
        # Excel 파일 읽기 (헤더 없이 먼저 읽어서 구조 파악)
        df_raw = pd.read_excel(input_file, sheet_name='Repair 서비스 단가표', engine='openpyxl', header=None)

        print(f"\n📋 원본 파일 정보:")
        print(f"데이터 형태: {df_raw.shape}")

        # 헤더 행 찾기 (11행 근처)
        header_row = 11
        print(f"\n헤더 행 ({header_row})의 내용:")
        header_data = df_raw.iloc[header_row].tolist()
        for i, val in enumerate(header_data[:10]):
            print(f"  열 {i}: {val}")

        # 실제 데이터는 12행부터 시작
        data_start_row = 12

        # 헤더 설정
        headers = df_raw.iloc[header_row].tolist()
        data_df = df_raw.iloc[data_start_row:].copy()
        data_df.columns = [f'col_{i}' if pd.isna(h) else str(h).replace('\n', '_').strip()
                          for i, h in enumerate(headers)]

        print(f"\n📋 데이터 정보:")
        print(f"데이터 형태: {data_df.shape}")
        print(f"컬럼 목록: {list(data_df.columns)[:10]}...")

        # 필요한 컬럼들 찾기
        target_columns = [
            '수리_부품교체(수리비용)',
            '수리_세척(신규)',
            '수리_S/W',
            '수리_기타',
            '검수(점검)'
        ]

        # 실제 존재하는 컬럼들 매핑
        column_mapping = {}
        for target_col in target_columns:
            for actual_col in data_df.columns:
                actual_col_clean = str(actual_col).replace('_', '').replace('(', '').replace(')', '').replace(' ', '')
                target_col_clean = target_col.replace('_', '').replace('(', '').replace(')', '').replace(' ', '')

                if target_col_clean in actual_col_clean or actual_col_clean in target_col_clean:
                    column_mapping[target_col] = actual_col
                    break

        print(f"\n🔍 컬럼 매핑:")
        for target, actual in column_mapping.items():
            print(f"  {target} -> {actual}")

        # 데이터가 있는 행만 필터링 (두 번째 컬럼에 제품명이 있는 행)
        second_col = data_df.columns[1]  # '제품군' 컬럼
        third_col = data_df.columns[2]   # 구분 컬럼
        fourth_col = data_df.columns[3]  # 세부구분 컬럼

        # 제품군이나 세부구분에 데이터가 있는 행 필터링
        valid_rows = data_df[
            (data_df[second_col].notna() & (data_df[second_col] != 'Tier 1') & (data_df[second_col] != 'Tier 2')) |
            (data_df[fourth_col].notna() & (data_df[fourth_col] != ''))
        ].copy()

        print(f"\n📈 유효한 데이터 행 수: {len(valid_rows)}")

        # 처음 5행 미리보기
        print(f"\n📋 유효 데이터 미리보기:")
        for i, (idx, row) in enumerate(valid_rows.head().iterrows()):
            print(f"  행 {i+1}: {[str(x)[:15] if pd.notna(x) else 'NaN' for x in row[:5].tolist()]}")
        
        # 결과 데이터프레임 생성
        result_data = []

        for idx, row in valid_rows.iterrows():
            row_result = {
                '제품군': row.iloc[1] if pd.notna(row.iloc[1]) else '',  # 두 번째 컬럼이 제품군
                '구분': row.iloc[2] if pd.notna(row.iloc[2]) else '',   # 세 번째 컬럼이 구분
                '세부구분': row.iloc[3] if pd.notna(row.iloc[3]) else '' # 네 번째 컬럼이 세부구분
            }

            # 각 컬럼별로 1%씩 차감 계산 (-20%까지)
            for target_col, actual_col in column_mapping.items():
                if actual_col in data_df.columns:
                    original_value = row[actual_col]

                    # 숫자 변환 시도
                    try:
                        if pd.notna(original_value):
                            if isinstance(original_value, str):
                                # 문자열에서 숫자 추출
                                import re
                                numbers = re.findall(r'\d+', str(original_value))
                                if numbers:
                                    original_value = float(numbers[0])
                                else:
                                    original_value = 0
                            elif isinstance(original_value, (int, float)):
                                original_value = float(original_value)
                            else:
                                original_value = 0
                        else:
                            original_value = 0
                    except:
                        original_value = 0

                    # 원본 값
                    row_result[f'{target_col}_원본'] = original_value

                    # 1%부터 20%까지 차감 계산
                    for percent in range(1, 21):
                        discount_rate = percent / 100
                        discounted_value = original_value * (1 - discount_rate)
                        row_result[f'{target_col}_{percent}%차감'] = round(discounted_value, 0)

            result_data.append(row_result)
        
        # 결과 데이터프레임 생성
        result_df = pd.DataFrame(result_data)
        
        print(f"\n📊 결과 데이터 형태: {result_df.shape}")
        
        # Excel 파일로 저장 (여러 시트)
        print(f"\n💾 결과 파일 저장 중...")
        
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 전체 결과 시트
            result_df.to_excel(writer, sheet_name='전체결과', index=False)
            
            # 각 컬럼별 요약 시트
            for target_col in column_mapping.keys():
                summary_data = []
                
                for idx, row in result_df.iterrows():
                    summary_row = {
                        '제품군': row['제품군'],
                        '구분': row['구분'],
                        '세부구분': row['세부구분'],
                        '원본값': row.get(f'{target_col}_원본', 0)
                    }
                    
                    # 1%부터 20%까지의 차감값
                    for percent in range(1, 21):
                        summary_row[f'{percent}%차감'] = row.get(f'{target_col}_{percent}%차감', 0)
                    
                    summary_data.append(summary_row)
                
                summary_df = pd.DataFrame(summary_data)
                sheet_name = target_col.replace('/', '_').replace('(', '').replace(')', '')[:31]  # 시트명 길이 제한
                summary_df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            # 요약 정보 시트
            summary_info = {
                '항목': [
                    '분석 대상 제품 수',
                    '분석 컬럼 수',
                    '차감 범위',
                    '차감 단위'
                ],
                '값': [
                    len(result_df),
                    len(column_mapping),
                    '1% ~ 20%',
                    '1%씩'
                ]
            }
            summary_info_df = pd.DataFrame(summary_info)
            summary_info_df.to_excel(writer, sheet_name='분석정보', index=False)
        
        print(f"✅ 파일 저장 완료: {output_file}")
        print(f"   📄 전체결과 시트: {result_df.shape}")
        print(f"   📄 컬럼별 요약 시트: {len(column_mapping)}개")
        print(f"   📄 분석정보 시트")
        
        # 결과 미리보기
        print(f"\n📋 결과 미리보기 (처음 5개 제품):")
        print("=" * 80)
        preview_cols = ['제품군', '구분', '세부구분']
        for target_col in list(column_mapping.keys())[:2]:  # 처음 2개 컬럼만
            preview_cols.extend([f'{target_col}_원본', f'{target_col}_5%차감', f'{target_col}_10%차감'])
        
        preview_df = result_df[preview_cols].head()
        print(preview_df.to_string(index=False))
        
        return result_df
        
    except Exception as e:
        print(f"❌ 오류 발생: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    print("=" * 70)
    print("📊 Tender 파일 분석 및 차감 계산 프로그램")
    print("=" * 70)
    
    # 메인 분석 실행
    result = analyze_tender_data()
    
    if result is not None:
        print(f"\n🎉 분석 완료!")
        print(f"   - 총 {len(result)}개 제품 분석")
        print(f"   - 1%부터 20%까지 차감값 계산")
        print(f"   - 결과 파일: tender_analysis_result.xlsx")
    
    print("\n" + "=" * 70)
    print("프로그램 종료")
    print("=" * 70)
