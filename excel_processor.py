#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel 파일 처리 프로그램
A열의 값들이 같은 것들을 그룹화하여 B열의 값에 누적하여 결과 파일을 생성합니다.
"""

import pandas as pd
import sys
from pathlib import Path

def process_excel_file(input_file, output_file):
    """
    Excel 파일을 처리하여 A열의 같은 값들을 그룹화하고 개수를 누적합니다.

    Args:
        input_file (str): 입력 Excel 파일 경로
        output_file (str): 출력 Excel 파일 경로
    """
    try:
        # Excel 파일 읽기
        print(f"'{input_file}' 파일을 읽는 중...")
        df = pd.read_excel(input_file)

        print(f"원본 데이터 행 수: {len(df)}")
        print(f"컬럼명: {df.columns.tolist()}")

        # 데이터 구조 확인 및 처리
        if len(df.columns) < 1:
            print("오류: 파일에 최소 1개의 컬럼이 필요합니다.")
            return False

        # 실제 데이터가 있는 컬럼 찾기
        data_column = None
        for col in df.columns:
            if not df[col].isna().all():  # NaN이 아닌 값이 있는 컬럼 찾기
                data_column = col
                break

        if data_column is None:
            print("오류: 유효한 데이터가 있는 컬럼을 찾을 수 없습니다.")
            return False

        print(f"데이터 컬럼: {data_column}")

        # 유효한 데이터만 추출 (NaN 제거)
        valid_data = df[data_column].dropna()
        print(f"유효한 데이터 행 수: {len(valid_data)}")

        if len(valid_data) == 0:
            print("오류: 유효한 데이터가 없습니다.")
            return False

        # 같은 값들을 그룹화하여 개수 계산
        result = valid_data.value_counts().reset_index()
        result.columns = ['A열_값', 'B열_개수']

        # 결과를 내림차순으로 정렬 (개수 기준)
        result = result.sort_values(by='B열_개수', ascending=False)

        print(f"\n처리 결과:")
        print(f"그룹화된 항목 수: {len(result)}")
        print(f"전체 데이터 개수: {result['B열_개수'].sum()}")
        print("\n상위 10개 결과:")
        print(result.head(10))

        # 결과를 Excel 파일로 저장
        print(f"\n'{output_file}' 파일로 저장 중...")
        result.to_excel(output_file, index=False)

        print(f"✅ 처리 완료! '{output_file}' 파일이 생성되었습니다.")
        return True

    except FileNotFoundError:
        print(f"오류: '{input_file}' 파일을 찾을 수 없습니다.")
        return False
    except Exception as e:
        print(f"오류 발생: {e}")
        return False

def main():
    """메인 함수"""
    input_file = "a.xlsx"
    output_file = "a_result.xlsx"
    
    print("=" * 50)
    print("Excel 파일 처리 프로그램")
    print("=" * 50)
    
    # 입력 파일 존재 확인
    if not Path(input_file).exists():
        print(f"오류: '{input_file}' 파일이 존재하지 않습니다.")
        sys.exit(1)
    
    # 파일 처리 실행
    success = process_excel_file(input_file, output_file)
    
    if success:
        print("\n🎉 프로그램이 성공적으로 완료되었습니다!")
    else:
        print("\n❌ 프로그램 실행 중 오류가 발생했습니다.")
        sys.exit(1)

if __name__ == "__main__":
    main()
